// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/product_bloc/product_bloc.dart';
import 'package:business_app/bloc/product_bloc/product_event.dart';
import 'package:business_app/bloc/product_bloc/product_state.dart';
import 'package:business_app/bloc/shop_bloc/shop_bloc.dart';
import 'package:business_app/bloc/shop_bloc/shop_event.dart';
import 'package:business_app/bloc/shop_bloc/shop_state.dart';
import 'package:business_app/ui/drawer_items/my_shop/add_product_page.dart';
import 'package:business_app/ui/drawer_items/my_shop/widgets/product_card.dart';
import 'package:business_app/ui/drawer_items/my_shop/widgets/empty_products_state.dart';

class MyShopPage extends StatefulWidget {
  const MyShopPage({super.key});

  @override
  State<MyShopPage> createState() => _MyShopPageState();
}

class _MyShopPageState extends State<MyShopPage> {
  @override
  void initState() {
    super.initState();
    context.read<ProductBloc>().add(const LoadProductsEvent());
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return BlocProvider(
      create: (context) => ShopBloc()..add(const LoadShopEvent()),
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: BlocBuilder<ShopBloc, ShopState>(
            builder: (context, state) {
              return Text(
                state.hasShop ? state.shop!.shopName : 'My Shop',
                style: const TextStyle(fontWeight: FontWeight.w500),
              );
            },
          ),
          centerTitle: true,
          elevation: 0,
          backgroundColor: Colors.transparent,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors:
                    isDark
                        ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
                        : [const Color(0xFF667eea), const Color(0xFF764ba2)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          foregroundColor: Colors.white,
          actions: [
            BlocBuilder<ProductBloc, ProductState>(
              builder: (context, state) {
                if (!state.hasProducts) return const SizedBox.shrink();
                return Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.search, color: Colors.white),
                      onPressed: () => _showSearchDialog(context),
                    ),
                    IconButton(
                      icon: const Icon(Icons.filter_list, color: Colors.white),
                      onPressed: () => _showFilterDialog(context),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        body: SafeArea(
          child: BlocConsumer<ProductBloc, ProductState>(
            listener: (context, state) {
              if (state.errorMessage != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage!),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    duration: const Duration(milliseconds: 800),
                  ),
                );
              }

              if (state.successMessage != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      state.successMessage!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    backgroundColor: Colors.blue,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    duration: const Duration(milliseconds: 800),
                  ),
                );
              }
            },
            builder: (context, state) {
              if (state.status == ProductStatus.loading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (!state.hasProducts) {
                return EmptyProductsState(
                  onAddProduct: () => _navigateToAddProduct(context),
                );
              }

              return Column(
                children: [
                  // Stats Header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors:
                            isDark
                                ? [
                                  const Color(0xFF1A1A2E),
                                  const Color(0xFF16213E),
                                ]
                                : [
                                  const Color(0xFF667eea),
                                  const Color(0xFF764ba2),
                                ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildQuickStat(
                            'Products',
                            state.totalProducts.toString(),
                            Icons.inventory_2,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildQuickStat(
                            'Categories',
                            state.categories.length.toString(),
                            Icons.category,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildQuickStat(
                            'Available',
                            state.availableProducts.toString(),
                            Icons.check_circle,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Grid of products
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
                      child: GridView.builder(
                        physics: const BouncingScrollPhysics(),
                        itemCount: state.displayProducts.length,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: _getCrossAxisCount(context),
                          childAspectRatio: 0.75,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                        ),
                        itemBuilder: (context, index) {
                          final product = state.displayProducts[index];
                          return ProductCard(
                            product: product,
                            onTap: () => _showProductDetails(context, product),
                            onEdit: () => _editProduct(context, product),
                            onDelete: () => _deleteProduct(context, product),
                            onToggleAvailability:
                                () => _toggleAvailability(context, product),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        floatingActionButton: BlocBuilder<ProductBloc, ProductState>(
          builder: (context, state) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(32),
                gradient: const LinearGradient(
                  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: FloatingActionButton.extended(
                onPressed: () => _navigateToAddProduct(context),
                backgroundColor: Colors.transparent,
                elevation: 0,
                icon: const Icon(Icons.add, color: Colors.white),
                label: const Text(
                  'Add Product',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 2;
  }

  void _navigateToAddProduct(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddProductPage()),
    );
  }

  void _showProductDetails(BuildContext context, product) {
    // TODO: Implement product details page
  }

  void _editProduct(BuildContext context, product) {
    // TODO: Implement edit product page
  }

  void _deleteProduct(BuildContext context, product) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Product'),
            content: Text('Are you sure you want to delete "${product.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  context.read<ProductBloc>().add(
                    DeleteProductEvent(product.id),
                  );
                  Navigator.pop(context);
                },
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  void _toggleAvailability(BuildContext context, product) {
    context.read<ProductBloc>().add(ToggleProductAvailabilityEvent(product.id));
  }

  void _showSearchDialog(BuildContext context) {
    // TODO: Implement search dialog
  }

  void _showFilterDialog(BuildContext context) {
    // TODO: Implement filter dialog
  }
}
